"use client";

import { store } from "@/store";
import { ReactNode, useEffect, Suspense } from "react";
import { Provider } from "react-redux";
import {
  WalletProvider,
  SocketProvider,
  MetadataProvider,
  NetworkProvider,
  useNetwork,
} from "@/context";
import Storage from "@/libs/storage";
import {
  setUserAuth,
  getFavouritePairs,
  getReferralMyCode,
  getSettingsCopyTradeOrder,
  getSettingsSnipeOrder,
  getSettingsQuickOrder,
  getSettingsLimitOrder,
} from "@/store/user.store";
import { useSearchParams } from "next/navigation";
import { setIsShowModalEnterCode } from "@/store/metadata.store";
import WalletExternalProvider from "./WalletProvider";

const SearchParamsHandler = () => {
  const searchParams = useSearchParams();

  useEffect(() => {
    if (searchParams.get("code")) {
      const code = searchParams.get("code");
      if (code) {
        store.dispatch(setIsShowModalEnterCode({ isShow: true }));
      }
    }
  }, [searchParams]);

  return null;
};

const extractReferralCodeFromQueryUrl = () => {
  if (typeof window === "undefined") {
    return null;
  }
  const urlParams = new URLSearchParams(window.location.search);
  const refCode = urlParams.get("ref");
  return refCode;
};

const extractReferralCodeFromCookie = () => {
  if (typeof window === "undefined") {
    return null;
  }

  // Đọc cookie referral_code
  const cookies = document.cookie.split(";");
  const referralCookie = cookies.find((cookie) =>
    cookie.trim().startsWith("referral_code=")
  );

  if (referralCookie) {
    return referralCookie.split("=")[1];
  }

  return null;
};

// Component that handles network-dependent logic after NetworkProvider is mounted
const NetworkDependentHandler = ({ children }: { children: ReactNode }) => {
  const accessToken = Storage.getAccessToken();
  const { currentNetwork } = useNetwork();

  useEffect(() => {
    if (!accessToken) return;
    // getPermissions().then();
    store.dispatch(getSettingsQuickOrder({ network: currentNetwork }));
    store.dispatch(getSettingsLimitOrder({ network: currentNetwork }));
    store.dispatch(getSettingsSnipeOrder({ network: currentNetwork }));
    store.dispatch(getSettingsCopyTradeOrder({ network: currentNetwork }));
    store.dispatch(getFavouritePairs({ network: currentNetwork }));
  }, [accessToken, currentNetwork]);

  useEffect(() => {
    if (!accessToken) return;
    // getPermissions().then();
    store.dispatch(getReferralMyCode());
  }, [accessToken, currentNetwork]);

  return <>{children}</>;
};

export const AppProvider = ({ children }: { children: ReactNode }) => {
  const accessToken = Storage.getAccessToken();

  if (accessToken) {
    store.dispatch(setUserAuth({ accessToken }));
    const redirectAfterLogin = Storage.getRedirectAfterLogin();
    if (redirectAfterLogin && typeof window !== "undefined") {
      const location = `${window.location.pathname}${window.location.search}`;
      if (location !== redirectAfterLogin) {
        Storage.clearRedirectAfterLogin();
        window.location.href = redirectAfterLogin;
      }
    }
  }

  const referralCodeFromQuery = extractReferralCodeFromQueryUrl();
  const referralCodeFromCookie = extractReferralCodeFromCookie();

  // Ưu tiên referral code từ query parameter, sau đó từ cookie
  const referralCode = referralCodeFromQuery || referralCodeFromCookie;

  if (referralCode && !Storage.getReferralCode()) {
    Storage.setReferralCode(referralCode);

    // Xóa cookie sau khi đã lưu vào storage để tránh xử lý lại
    if (referralCodeFromCookie) {
      document.cookie =
        "referral_code=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    }
  }

  // const getPermissions = useCallback(async () => {
  //   try {
  //     const permissions = await rf.getRequest("OauthRequest").getPermissions();
  //     store.dispatch(setPermissions(permissions));
  //   } catch (error: any) {
  //     console.error(`Failed to get permissions:`, error?.message);
  //   }
  // }, []);

  return (
    <Provider store={store}>
      <NetworkProvider>
        <NetworkDependentHandler>
          <WalletExternalProvider>
            <SocketProvider accessToken={accessToken}>
              <MetadataProvider>
                <WalletProvider>
                  <Suspense fallback={null}>
                    <SearchParamsHandler />
                  </Suspense>
                  {children}
                </WalletProvider>
              </MetadataProvider>
            </SocketProvider>
          </WalletExternalProvider>
        </NetworkDependentHandler>
      </NetworkProvider>
    </Provider>
  );
};
